package com.tui.destilink.framework.test.support.postgresql;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestProfile;
import com.tui.destilink.framework.test.support.postgresql.service.PostgresTestSupportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.test.context.ContextCustomizer;
import org.springframework.test.context.MergedContextConfiguration;

import javax.sql.DataSource;
import java.util.List;

/**
 * Context customizer for PostgreSQL test support.
 */
@Slf4j
@RequiredArgsConstructor
public class TestSupportPostgresqlContextCustomizer implements ContextCustomizer, Ordered {

    private final List<PostgresTestSupport> postgresTestSupports;
    private final String uniqueId;

    public TestSupportPostgresqlContextCustomizer(PostgresTestSupport postgresTestSupport, String uniqueId) {
        this(List.of(postgresTestSupport), uniqueId);
    }

    @Override
    public void customizeContext(@NonNull ConfigurableApplicationContext context,
            @NonNull MergedContextConfiguration mergedConfig) {
        log.debug("Customizing context for PostgreSQL test support with {} configuration(s)",
                postgresTestSupports.size());

        // Register test class ID as system property for backward compatibility
        System.setProperty("test.postgresql.test-class-id", uniqueId);
        log.debug("Registered test-class-id: {}", uniqueId);

        // Create qualified DataSource beans for multiple profiles
        if (postgresTestSupports.size() > 1) {
            createMultipleDataSourceBeans(context);
        }
    }

    private void createMultipleDataSourceBeans(ConfigurableApplicationContext context) {
        DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory) context.getBeanFactory();
        PostgresTestSupportService service = context.getBean(PostgresTestSupportService.class);

        for (PostgresTestSupport support : postgresTestSupports) {
            PostgresTestProfile profile = support.profile();
            if (!profile.isDefault()) {
                String qualifier = profile.getQualifier();
                String beanName = qualifier + "DataSource";

                log.debug("Creating qualified DataSource bean: {} for profile: {}", beanName, qualifier);

                // Create DataSource bean definition
                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSource.class);
                builder.setFactoryMethodOnBean("createDataSourceForProfile", "postgresTestSupportService");
                builder.addConstructorArgValue(profile.getQualifier());
                builder.addConstructorArgValue(uniqueId);

                // Register the bean with qualifier
                beanFactory.registerBeanDefinition(beanName, builder.getBeanDefinition());
            }
        }
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 100;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;

        TestSupportPostgresqlContextCustomizer that = (TestSupportPostgresqlContextCustomizer) obj;
        return postgresTestSupports.equals(that.postgresTestSupports) && uniqueId.equals(that.uniqueId);
    }

    @Override
    public int hashCode() {
        return postgresTestSupports.hashCode() * 31 + uniqueId.hashCode();
    }
}