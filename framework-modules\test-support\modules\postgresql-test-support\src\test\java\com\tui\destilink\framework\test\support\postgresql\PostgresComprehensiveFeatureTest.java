package com.tui.destilink.framework.test.support.postgresql;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import com.tui.destilink.framework.test.support.postgresql.data.PostgresTestDataGenerator;
import com.tui.destilink.framework.test.support.postgresql.monitoring.PostgresTestMonitor;
import com.tui.destilink.framework.test.support.postgresql.scenarios.PostgresTestScenarios;
import com.tui.destilink.framework.test.support.postgresql.validation.PostgresTestValidator;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive test demonstrating all advanced PostgreSQL test support
 * features.
 * <p>
 * This test showcases the complete feature set of the PostgreSQL test support
 * module:
 * <ul>
 * <li>Advanced test data generation with realistic data</li>
 * <li>Comprehensive monitoring and metrics collection</li>
 * <li>Advanced test scenarios and performance testing</li>
 * <li>Sophisticated validation framework</li>
 * <li>Integration of all utility classes</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@SpringBootTest(classes = com.tui.destilink.framework.test.support.postgresql.test.TestApplication.class)
@PostgresTestSupport(databaseNamePrefix = "comprehensive_feature_test", userNamePrefix = "comprehensive_feature_user", enableFlyway = false, cleanupOnStart = true, cleanupOnShutdown = true)
@TestPropertySource(properties = {
        "destilink.fw.test-support.postgresql.enabled=true",
        "destilink.fw.test-support.postgresql.enable-sql-logging=false"
})
class PostgresComprehensiveFeatureTest {

    @Autowired
    private DataSource dataSource;

    private PostgresTestScenarios scenarios;
    private PostgresTestMonitor monitor;

    @AfterEach
    void cleanup() {
        if (scenarios != null) {
            scenarios.shutdown();
        }
    }

    @Test
    void shouldDemonstrateAdvancedDataGeneration() {
        System.out.println("\n=== ADVANCED DATA GENERATION DEMO ===\n");

        var dataGenerator = PostgresTestDataGenerator.forDataSource(dataSource);

        // Generate realistic user data
        int userCount = dataGenerator.users(50)
                .table("demo_users")
                .withAddresses()
                .withPhones()
                .generate();

        assertThat(userCount).isEqualTo(50);
        System.out.println("✅ Generated " + userCount + " realistic users with addresses and phones");

        // Generate realistic product data
        int productCount = dataGenerator.products(100)
                .table("demo_products")
                .generate();

        assertThat(productCount).isEqualTo(100);
        System.out.println("✅ Generated " + productCount + " realistic products");

        // Generate orders with relationships
        int orderCount = dataGenerator.orders(200)
                .table("demo_orders")
                .userTable("demo_users")
                .generate();

        assertThat(orderCount).isEqualTo(200);
        System.out.println("✅ Generated " + orderCount + " orders with user relationships");

        // Ensure demo_analytics table exists before generating custom data
        var executor = PostgresTestUtils.executor(dataSource);
        executor.update("CREATE TABLE IF NOT EXISTS demo_analytics (event_id VARCHAR(64) PRIMARY KEY, event_type VARCHAR(32), user_score INTEGER, conversion_rate DOUBLE PRECISION, event_date DATE)");

        // Generate custom data
        int customCount = dataGenerator.custom(75)
                .table("demo_analytics")
                .field("event_id", () -> "EVT_" + System.currentTimeMillis())
                .stringField("event_type", Arrays.asList("click", "view", "purchase", "signup"))
                .intField("user_score", 1, 100)
                .doubleField("conversion_rate", 0.0, 1.0)
                .dateField("event_date", LocalDate.now().minusDays(30), LocalDate.now())
                .generate();

        assertThat(customCount).isEqualTo(75);
        System.out.println("✅ Generated " + customCount + " custom analytics records");

        // Verify data relationships

        // Check user-order relationships
        String relationshipQuery = """
                SELECT u.first_name, u.last_name, COUNT(o.id) as order_count
                FROM demo_users u
                LEFT JOIN demo_orders o ON u.id = o.user_id
                GROUP BY u.id, u.first_name, u.last_name
                HAVING COUNT(o.id) > 0
                ORDER BY order_count DESC
                LIMIT 5
                """;

        var topCustomers = executor.query(relationshipQuery);
        assertThat(topCustomers).isNotEmpty();
        System.out.println("✅ Verified user-order relationships: " + topCustomers.size() + " customers with orders");

        System.out.println("\n🎉 Advanced data generation completed successfully!\n");
    }

    @Test
    void shouldDemonstrateComprehensiveMonitoring() {
        System.out.println("\n=== COMPREHENSIVE MONITORING DEMO ===\n");

        monitor = PostgresTestMonitor.forDataSource(dataSource);

        // Create test data for monitoring
        PostgresTestUtils.schemaManager(dataSource)
                .createTable("monitoring_test",
                        "id SERIAL PRIMARY KEY",
                        "name VARCHAR(100)",
                        "value INTEGER",
                        "created_at TIMESTAMP DEFAULT NOW()");

        // Perform monitored operations
        var executor = PostgresTestUtils.executor(dataSource);

        // Record query operations
        long queryStart = System.currentTimeMillis();
        var results = executor.query("SELECT * FROM monitoring_test");
        long queryDuration = System.currentTimeMillis() - queryStart;
        monitor.recordQuery(queryDuration, results.size());

        // Record update operations
        long updateStart = System.currentTimeMillis();
        int affected = executor.update("INSERT INTO monitoring_test (name, value) VALUES (?, ?)", "test", 42);
        long updateDuration = System.currentTimeMillis() - updateStart;
        monitor.recordUpdate(updateDuration, affected);

        // Record transaction operations
        long txStart = System.currentTimeMillis();
        executor.executeInTransaction(txExecutor -> {
            txExecutor.update("INSERT INTO monitoring_test (name, value) VALUES (?, ?)", "tx1", 1);
            txExecutor.update("INSERT INTO monitoring_test (name, value) VALUES (?, ?)", "tx2", 2);
            return null;
        });
        long txDuration = System.currentTimeMillis() - txStart;
        monitor.recordTransaction(txDuration, 2);

        // Get monitoring metrics
        Map<String, Object> metrics = monitor.getMetrics();
        assertThat(metrics).containsKey("queries_executed");
        assertThat(metrics).containsKey("updates_executed");
        assertThat(metrics).containsKey("transactions_executed");
        assertThat(metrics).containsKey("queries_per_second");

        System.out.println("📊 Performance Metrics:");
        System.out.println("  - Queries executed: " + metrics.get("queries_executed"));
        System.out.println("  - Updates executed: " + metrics.get("updates_executed"));
        System.out.println("  - Transactions executed: " + metrics.get("transactions_executed"));
        System.out.println("  - Queries per second: " + String.format("%.2f", metrics.get("queries_per_second")));

        // Get database health
        Map<String, Object> health = monitor.getDatabaseHealth();
        assertThat(health).containsKey("connection_status");
        assertThat(health.get("connection_status")).isEqualTo("healthy");

        System.out.println("🏥 Database Health:");
        System.out.println("  - Connection status: " + health.get("connection_status"));
        System.out.println("  - Database size: " + health.get("database_size"));
        System.out.println("  - Active connections: " + health.get("active_connections"));
        System.out.println("  - Table count: " + health.get("table_count"));

        // Get comprehensive report
        var report = monitor.getReport();
        assertThat(report.getPerformanceMetrics()).isNotEmpty();
        assertThat(report.getDatabaseHealth()).isNotEmpty();
        assertThat(report.getUptime()).isNotNull();

        System.out.println("📋 Monitoring Report: " + report);
        System.out.println("\n🎉 Comprehensive monitoring completed successfully!\n");
    }

    @Test
    void shouldDemonstrateAdvancedValidation() {
        System.out.println("\n=== ADVANCED VALIDATION DEMO ===\n");

        // Create test schema for validation
        PostgresTestUtils.schemaManager(dataSource)
                .createTable("validation_users",
                        "id SERIAL PRIMARY KEY",
                        "email VARCHAR(100) UNIQUE NOT NULL",
                        "age INTEGER CHECK (age >= 18 AND age <= 120)",
                        "score DECIMAL(5,2) CHECK (score >= 0.0 AND score <= 100.0)",
                        "created_at TIMESTAMP DEFAULT NOW()")
                .createTable("validation_orders",
                        "id SERIAL PRIMARY KEY",
                        "user_id INTEGER NOT NULL",
                        "amount DECIMAL(10,2) NOT NULL CHECK (amount > 0)",
                        "status VARCHAR(20) DEFAULT 'pending'")
                .addForeignKey("validation_orders", "user_id", "validation_users", "id");

        // Insert test data
        var executor = PostgresTestUtils.executor(dataSource);
        executor.update("INSERT INTO validation_users (email, age, score) VALUES (?, ?, ?)", "<EMAIL>", 25,
                85.5);
        executor.update("INSERT INTO validation_users (email, age, score) VALUES (?, ?, ?)", "<EMAIL>", 30,
                92.0);
        executor.update("INSERT INTO validation_users (email, age, score) VALUES (?, ?, ?)", "<EMAIL>", 28,
                78.3);

        executor.update("INSERT INTO validation_orders (user_id, amount, status) VALUES (?, ?, ?)", 1, 99.99,
                "completed");
        executor.update("INSERT INTO validation_orders (user_id, amount, status) VALUES (?, ?, ?)", 2, 149.50,
                "pending");
        executor.update("INSERT INTO validation_orders (user_id, amount, status) VALUES (?, ?, ?)", 1, 75.25,
                "shipped");

        // Comprehensive validation
        var validator = PostgresTestValidator.forDataSource(dataSource)
                // Schema validation
                .tableExists("validation_users")
                .tableExists("validation_orders")
                .columnExists("validation_users", "email")
                .columnExists("validation_users", "age")
                .columnExists("validation_orders", "user_id")

                // Data validation
                .rowCount("validation_users", 3)
                .rowCount("validation_orders", 3)
                .rowCountBetween("validation_users", 1, 10)

                // Data integrity validation
                .uniqueValues("validation_users", "email")
                .noNullValues("validation_users", "email")
                .noNullValues("validation_orders", "user_id")

                // Pattern validation
                .valuesMatchPattern("validation_users", "email", "^[\\w._%+-]+@[\\w.-]+\\.[A-Za-z]{2,}$")
                .valuesMatchPattern("validation_orders", "status", "^(pending|processing|shipped|completed|cancelled)$")

                // Range validation
                .valuesInRange("validation_users", "age", 18, 120)
                .valuesInRange("validation_users", "score", 0.0, 100.0)
                .valuesInRange("validation_orders", "amount", 0.01, 10000.0)

                // Relationship validation
                .foreignKeyExists("validation_orders", "user_id", "validation_users", "id")

                // Custom validation
                .customQuery("Average user age should be reasonable",
                        "SELECT AVG(age) FROM validation_users", 27.67)
                .custom("All orders should have positive amounts",
                        ds -> PostgresTestUtils.executor(ds).queryForObject(
                                "SELECT COUNT(*) FROM validation_orders WHERE amount <= 0", Integer.class) == 0);

        // Execute validation
        var results = validator.validate();

        System.out.println("🔍 Validation Results:");
        System.out.println("  - Total validations: " + results.getTotalCount());
        System.out.println("  - Passed: " + results.getSuccessCount());
        System.out.println("  - Failed: " + results.getFailureCount());
        System.out.println("  - Success rate: " + String.format("%.1f%%", results.getSuccessRate() * 100));

        // Display detailed results
        results.getResults().forEach(result -> {
            String status = result.isSuccess() ? "✅" : "❌";
            System.out.println("  " + status + " " + result.getDescription());
            if (!result.isSuccess()) {
                System.out.println("      Error: " + result.getErrorMessage());
            }
        });

        // Assert overall success
        assertThat(results.allPassed()).isTrue();
        assertThat(results.getSuccessRate()).isEqualTo(1.0);

        System.out.println("\n🎉 Advanced validation completed successfully!\n");
    }

    @Test
    void shouldDemonstrateIntegratedWorkflow() {
        System.out.println("\n=== INTEGRATED WORKFLOW DEMO ===\n");

        // Initialize all components
        var dataGenerator = PostgresTestDataGenerator.forDataSource(dataSource);
        monitor = PostgresTestMonitor.forDataSource(dataSource);
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        // Step 1: Generate comprehensive test data
        System.out.println("📊 Step 1: Generating comprehensive test data...");

        int userCount = dataGenerator.users(25)
                .table("workflow_users")
                .withAddresses()
                .generate();

        int productCount = dataGenerator.products(50)
                .table("workflow_products")
                .generate();

        int orderCount = dataGenerator.orders(100)
                .table("workflow_orders")
                .userTable("workflow_users")
                .generate();

        System.out.println(
                "  ✅ Generated " + userCount + " users, " + productCount + " products, " + orderCount + " orders");

        // Step 2: Perform advanced test scenarios
        System.out.println("🧪 Step 2: Running advanced test scenarios...");

        var performanceResult = scenarios.performance().testQueryPerformance(20, 10);
        assertThat(performanceResult.isSuccess()).isTrue();

        var concurrencyResult = scenarios.concurrentAccess().testConcurrentReads(3, 10);
        assertThat(concurrencyResult.isSuccess()).isTrue();

        System.out.println("  ✅ Performance test: " + performanceResult.getMessage());
        System.out.println("  ✅ Concurrency test: " + concurrencyResult.getMessage());

        // Step 3: Comprehensive validation
        System.out.println("🔍 Step 3: Comprehensive validation...");

        var validationResults = PostgresTestValidator.forDataSource(dataSource)
                .tableExists("workflow_users")
                .tableExists("workflow_products")
                .tableExists("workflow_orders")
                .rowCount("workflow_users", userCount)
                .rowCount("workflow_products", productCount)
                .rowCount("workflow_orders", orderCount)
                .foreignKeyExists("workflow_orders", "user_id", "workflow_users", "id")
                .validate();

        assertThat(validationResults.allPassed()).isTrue();
        System.out.println("  ✅ All " + validationResults.getSuccessCount() + " validations passed");

        // Step 4: Monitoring and metrics
        System.out.println("📈 Step 4: Collecting metrics and monitoring data...");

        var metrics = monitor.getMetrics();
        var health = monitor.getDatabaseHealth();

        System.out.println("  📊 Performance metrics collected: " + metrics.size() + " metrics");
        System.out.println("  🏥 Database health: " + health.get("connection_status"));

        // Step 5: Final comprehensive assertions
        System.out.println("✅ Step 5: Final verification...");

        // Verify data integrity across all tables
        var executor = PostgresTestUtils.executor(dataSource);

        String integrityQuery = """
                SELECT
                    (SELECT COUNT(*) FROM workflow_users) as user_count,
                    (SELECT COUNT(*) FROM workflow_products) as product_count,
                    (SELECT COUNT(*) FROM workflow_orders) as order_count,
                    (SELECT COUNT(*) FROM workflow_orders o JOIN workflow_users u ON o.user_id = u.id) as valid_orders
                """;

        var integrityResults = executor.query(integrityQuery);
        assertThat(integrityResults).hasSize(1);

        Map<String, Object> integrity = integrityResults.get(0);
        assertThat(integrity.get("user_count")).isEqualTo((long) userCount);
        assertThat(integrity.get("product_count")).isEqualTo((long) productCount);
        assertThat(integrity.get("order_count")).isEqualTo((long) orderCount);
        assertThat(integrity.get("valid_orders")).isEqualTo((long) orderCount);

        System.out.println("  ✅ Data integrity verified across all tables");
        System.out.println("  ✅ All foreign key relationships valid");

        System.out.println("\n🎉 INTEGRATED WORKFLOW COMPLETED SUCCESSFULLY! 🎉");
        System.out.println("📋 Summary:");
        System.out.println("  - Data generation: " + (userCount + productCount + orderCount) + " records");
        System.out.println("  - Test scenarios: 2 scenarios executed");
        System.out.println("  - Validations: " + validationResults.getSuccessCount() + " validations passed");
        System.out.println("  - Monitoring: " + metrics.size() + " metrics collected");
        System.out.println("  - Database health: " + health.get("connection_status"));
        System.out.println("\n");
    }
}
